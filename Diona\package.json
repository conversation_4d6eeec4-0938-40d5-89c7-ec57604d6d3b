{"name": "diona-document-verification-service", "version": "1.0.0", "description": "AI-powered document verification microservice for Diona mobile app registration flow", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["document-verification", "ai", "openai", "gpt-4o", "microservice", "node.js", "express"], "author": "Diona Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "openai": "^4.65.3", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "pdf-poppler": "^0.2.1", "sharp": "^0.33.0", "tesseract.js": "^5.0.4", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8", "eslint": "^8.54.0", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}}