# Diona Document Verification Service

An AI-powered microservice for verifying document authenticity and type matching using OpenAI GPT-4o vision capabilities. This service enhances the document upload step in the Diona mobile app's registration flow by analyzing document content and structure to determine authenticity and match with claimed types.

## Features

- **Document Type Verification**: Validates if uploaded documents match user-selected types
- **Multi-format Support**: Handles PDF, JPG, PNG document formats
- **AI-Powered Analysis**: Uses OpenAI GPT-4o for multimodal document analysis
- **Field Extraction**: Extracts key information like name, ID number, date of birth
- **Confidence Scoring**: Provides confidence scores (0-100%) for verification results
- **Multiple Document Types**: Supports passports, driver's licenses, national IDs, and more
- **International Support**: Works with documents from multiple countries
- **Rate Limiting**: Built-in protection against abuse
- **Comprehensive Logging**: Detailed logging for monitoring and debugging

## Supported Document Types

- Indian Passport
- US Driver's License
- UK National ID
- Aadhaar Card
- PAN Card
- US Passport
- Canadian Driver's License
- Australian Passport
- German ID Card
- French National ID
- Japanese Passport

## API Endpoints

### POST /api/v1/documents/verify
Verify if an uploaded document matches the selected document type.

**Request:**
- `documentType` (string, required): The expected document type
- `country` (string, optional): The expected country of origin
- `document` (file, required): The document file to verify

**Response:**
```json
{
  "success": true,
  "data": {
    "isMatch": true,
    "confidence": 95,
    "detectedType": "Indian Passport",
    "detectedCountry": "India",
    "extractedFields": {
      "name": "John Doe",
      "passportNumber": "********",
      "dateOfBirth": "1990-01-01",
      "nationality": "Indian"
    }
  }
}
```

### GET /api/v1/documents/supported-types
Get list of supported document types and countries.

### POST /api/v1/documents/extract-fields
Extract key fields from a document without type verification.

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd diona-document-verification
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start the service:
```bash
# Development
npm run dev

# Production
npm start
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment mode | development |
| `OPENAI_API_KEY` | OpenAI API key | required |
| `OPENAI_MODEL` | OpenAI model to use | gpt-4o |
| `MAX_FILE_SIZE` | Maximum file size in bytes | 10485760 (10MB) |
| `ALLOWED_FILE_TYPES` | Comma-separated file extensions | pdf,jpg,jpeg,png |

## Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Architecture

The service follows a modular architecture:

- **Routes**: Handle HTTP requests and responses
- **Controllers**: Business logic for document processing
- **Services**: AI integration and document analysis
- **Middleware**: Authentication, validation, error handling
- **Utils**: Helper functions and utilities

## Security Features

- Rate limiting to prevent abuse
- File type validation
- File size limits
- CORS protection
- Security headers via Helmet
- Input validation and sanitization

## Logging

The service uses Winston for comprehensive logging:
- Request/response logging
- Error tracking
- Performance monitoring
- Configurable log levels

## License

MIT License - see LICENSE file for details.
