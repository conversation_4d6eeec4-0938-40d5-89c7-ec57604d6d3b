/**
 * Document templates defining expected characteristics for different document types
 * These templates help validate document authenticity and extract relevant fields
 */

const documentTemplates = {
  'Indian Passport': {
    country: 'India',
    type: 'passport',
    expectedFields: [
      'name', 'passportNumber', 'nationality', 'dateOfBirth',
      'placeOfBirth', 'dateOfIssue', 'dateOfExpiry', 'issuingAuthority'
    ],
    requiredFields: ['name', 'passportNumber', 'nationality', 'dateOfBirth'],
    securityFeatures: [
      'Government of India emblem',
      'Security watermarks',
      'Machine-readable zone (MRZ)',
      'Holographic elements',
      'Special security paper'
    ],
    layoutCharacteristics: {
      hasPhoto: true,
      hasMRZ: true,
      pageCount: 36,
      coverColor: 'dark blue',
      languages: ['English', 'Hindi']
    },
    validationRules: {
      passportNumber: {
        pattern: /^[A-Z]\d{7}$/,
        description: 'One letter followed by 7 digits'
      },
      nationality: {
        expectedValue: 'INDIAN',
        description: 'Must be INDIAN'
      }
    },
    commonAnomalies: [
      'Missing security features',
      'Incorrect passport number format',
      'Wrong issuing authority',
      'Inconsistent fonts or layouts'
    ]
  },

  'US Driver\'s License': {
    country: 'United States',
    type: 'drivers_license',
    expectedFields: [
      'name', 'licenseNumber', 'address', 'dateOfBirth',
      'dateOfIssue', 'dateOfExpiry', 'restrictions', 'class'
    ],
    requiredFields: ['name', 'licenseNumber', 'dateOfBirth', 'address'],
    securityFeatures: [
      'State-specific design elements',
      'Holographic overlays',
      'REAL ID compliance star (if applicable)',
      'Magnetic stripe or barcode',
      'Special inks and materials'
    ],
    layoutCharacteristics: {
      hasPhoto: true,
      hasMRZ: false,
      orientation: 'landscape',
      standardSize: 'CR80 (credit card size)'
    },
    validationRules: {
      licenseNumber: {
        pattern: /^[A-Z0-9]{8,12}$/,
        description: 'Alphanumeric, 8-12 characters (varies by state)'
      }
    },
    stateVariations: true,
    commonAnomalies: [
      'Non-standard dimensions',
      'Missing state-specific elements',
      'Incorrect license number format',
      'Poor photo quality'
    ]
  },

  'UK National ID': {
    country: 'United Kingdom',
    type: 'national_id',
    expectedFields: [
      'name', 'documentNumber', 'nationality', 'dateOfBirth',
      'dateOfIssue', 'dateOfExpiry', 'issuingAuthority'
    ],
    requiredFields: ['name', 'documentNumber', 'nationality', 'dateOfBirth'],
    securityFeatures: [
      'UK government crest',
      'Security holograms',
      'Special security paper',
      'Microprinting',
      'UV-reactive inks'
    ],
    layoutCharacteristics: {
      hasPhoto: true,
      hasMRZ: false,
      cardFormat: true,
      languages: ['English']
    },
    validationRules: {
      nationality: {
        expectedValues: ['BRITISH', 'BRITISH CITIZEN'],
        description: 'Must indicate British nationality'
      }
    },
    commonAnomalies: [
      'Missing security features',
      'Incorrect government branding',
      'Wrong card dimensions',
      'Poor print quality'
    ]
  },

  'Aadhaar Card': {
    country: 'India',
    type: 'national_id',
    expectedFields: [
      'name', 'documentNumber', 'dateOfBirth', 'gender', 'address'
    ],
    requiredFields: ['name', 'documentNumber', 'dateOfBirth'],
    securityFeatures: [
      'UIDAI logo',
      'Security holograms',
      'QR code',
      'Special security paper',
      'Microtext'
    ],
    layoutCharacteristics: {
      hasPhoto: true,
      hasMRZ: false,
      cardFormat: true,
      languages: ['English', 'Hindi', 'Local language']
    },
    validationRules: {
      documentNumber: {
        pattern: /^\d{4}\s\d{4}\s\d{4}$/,
        description: '12 digits in XXXX XXXX XXXX format'
      }
    },
    commonAnomalies: [
      'Incorrect Aadhaar number format',
      'Missing UIDAI branding',
      'Poor QR code quality',
      'Inconsistent language usage'
    ]
  },

  'US Passport': {
    country: 'United States',
    type: 'passport',
    expectedFields: [
      'name', 'passportNumber', 'nationality', 'dateOfBirth',
      'placeOfBirth', 'dateOfIssue', 'dateOfExpiry', 'issuingAuthority'
    ],
    requiredFields: ['name', 'passportNumber', 'nationality', 'dateOfBirth'],
    securityFeatures: [
      'US Department of State seal',
      'Security watermarks',
      'Machine-readable zone (MRZ)',
      'RFID chip (for newer passports)',
      'Special security paper'
    ],
    layoutCharacteristics: {
      hasPhoto: true,
      hasMRZ: true,
      pageCount: 28,
      coverColor: 'dark blue',
      languages: ['English']
    },
    validationRules: {
      passportNumber: {
        pattern: /^\d{9}$/,
        description: '9 digits'
      },
      nationality: {
        expectedValue: 'UNITED STATES OF AMERICA',
        description: 'Must be UNITED STATES OF AMERICA'
      }
    },
    commonAnomalies: [
      'Missing security features',
      'Incorrect passport number format',
      'Wrong issuing authority format',
      'Missing or damaged RFID chip indicator'
    ]
  },

  'Canadian Driver\'s License': {
    country: 'Canada',
    type: 'drivers_license',
    expectedFields: [
      'name', 'licenseNumber', 'address', 'dateOfBirth',
      'dateOfIssue', 'dateOfExpiry', 'class', 'restrictions'
    ],
    requiredFields: ['name', 'licenseNumber', 'dateOfBirth', 'address'],
    securityFeatures: [
      'Provincial/territorial specific design',
      'Holographic elements',
      'Special security features',
      'Magnetic stripe or barcode'
    ],
    layoutCharacteristics: {
      hasPhoto: true,
      hasMRZ: false,
      orientation: 'landscape',
      bilingualText: true // English and French
    },
    validationRules: {
      licenseNumber: {
        pattern: /^[A-Z0-9]{10,15}$/,
        description: 'Alphanumeric, varies by province/territory'
      }
    },
    provincialVariations: true,
    commonAnomalies: [
      'Missing provincial branding',
      'Incorrect bilingual text',
      'Wrong license format',
      'Missing security features'
    ]
  },

  'PAN Card': {
    country: 'India',
    type: 'tax_id',
    expectedFields: [
      'name', 'documentNumber', 'dateOfBirth', 'fatherName'
    ],
    requiredFields: ['name', 'documentNumber'],
    securityFeatures: [
      'Income Tax Department logo',
      'Government of India emblem',
      'Security holograms',
      'Special security paper',
      'Laminated finish',
      'Unique PAN number'
    ],
    layoutCharacteristics: {
      hasPhoto: true,
      hasMRZ: false,
      cardFormat: true,
      orientation: 'landscape',
      standardSize: 'CR80 (credit card size)',
      languages: ['English', 'Hindi']
    },
    validationRules: {
      documentNumber: {
        pattern: /^[A-Z]{5}\d{4}[A-Z]$/,
        description: '10 characters: 5 letters, 4 digits, 1 letter (e.g., **********)'
      }
    },
    commonAnomalies: [
      'Incorrect PAN number format',
      'Missing Income Tax Department branding',
      'Poor lamination quality',
      'Inconsistent font usage',
      'Missing security features',
      'Wrong card dimensions'
    ]
  }
};

/**
 * Get document template by type
 * @param {string} documentType - Document type
 * @returns {Object|null} Document template or null if not found
 */
function getDocumentTemplate(documentType) {
  return documentTemplates[documentType] || null;
}

/**
 * Get all supported document types
 * @returns {Array} Array of supported document types
 */
function getSupportedDocumentTypes() {
  return Object.keys(documentTemplates);
}

/**
 * Get supported countries
 * @returns {Array} Array of supported countries
 */
function getSupportedCountries() {
  const countries = new Set();
  Object.values(documentTemplates).forEach(template => {
    countries.add(template.country);
  });
  return Array.from(countries).sort();
}

/**
 * Get document types by country
 * @param {string} country - Country name
 * @returns {Array} Array of document types for the country
 */
function getDocumentTypesByCountry(country) {
  return Object.entries(documentTemplates)
    .filter(([_, template]) => template.country === country)
    .map(([type, _]) => type);
}

/**
 * Validate field against template rules
 * @param {string} documentType - Document type
 * @param {string} fieldName - Field name
 * @param {string} fieldValue - Field value
 * @returns {Object} Validation result
 */
function validateField(documentType, fieldName, fieldValue) {
  const template = getDocumentTemplate(documentType);
  if (!template || !template.validationRules || !template.validationRules[fieldName]) {
    return { isValid: true, message: 'No validation rule defined' };
  }

  const rule = template.validationRules[fieldName];
  
  if (rule.pattern && !rule.pattern.test(fieldValue)) {
    return {
      isValid: false,
      message: `${fieldName} format is invalid. Expected: ${rule.description}`
    };
  }

  if (rule.expectedValue && fieldValue !== rule.expectedValue) {
    return {
      isValid: false,
      message: `${fieldName} value is incorrect. Expected: ${rule.expectedValue}`
    };
  }

  if (rule.expectedValues && !rule.expectedValues.includes(fieldValue)) {
    return {
      isValid: false,
      message: `${fieldName} value is incorrect. Expected one of: ${rule.expectedValues.join(', ')}`
    };
  }

  return { isValid: true, message: 'Field validation passed' };
}

module.exports = {
  documentTemplates,
  getDocumentTemplate,
  getSupportedDocumentTypes,
  getSupportedCountries,
  getDocumentTypesByCountry,
  validateField
};
